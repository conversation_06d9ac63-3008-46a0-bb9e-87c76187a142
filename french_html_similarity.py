import itertools

# https://github.com/GateNLP/ultimate-sitemap-parser
from usp.tree import sitemap_tree_for_homepage
from usp.web_client.requests_client import RequestsWebClient
from usp.helpers import ungzipped_response_content
from usp.web_client.abstract_client import WebClientErrorResponse

# https://github.com/UKPLab/sentence-transformers
from sentence_transformers import SentenceTransformer, util

# https://github.com/trafilatura/trafilatura
import trafilatura

# https://github.com/networkx/networkx
import networkx as nx

# https://github.com/Textualize/rich
from rich.console import Console
from rich.progress import (
    Progress,
    SpinnerColumn,
    TextColumn,
    BarColumn,
    TaskProgressColumn,
)
from rich.panel import Panel

#
import requests

import asyncio
from pydoll.browser import Chrome

async def scrape_page(url, tab):
    await tab.go_to(url)
    title = await tab.execute_script('return document.title')
    links = await tab.find(tag_name='a', find_all=True)
    return {
        'url': url,
        'title': title,
        'link_count': len(links)
    }

async def concurrent_scraping(urls):
    browser = Chrome()
    tab_google = await browser.start()
    tab_duckduckgo = await browser.new_tab()
    tasks = [
        scrape_page('https://google.com/', tab_google),
        scrape_page('https://duckduckgo.com/', tab_duckduckgo)
    ]
    results = await asyncio.gather(*tasks)
    print(results)
    await browser.stop()


if __name__ == "__main__":
    console = Console()

    console.print(
        Panel.fit("🚀 Analyseur de Similarité HTML Français", style="bold magenta")
    )

    console.print("[cyan]📡 Récupération des URLs depuis le sitemap...[/cyan]")
    links = []
    # sitemap_tree = sitemap_tree_for_homepage("https://zonetuto.fr/")
    sitemap_tree = sitemap_tree_for_homepage(
        homepage_url="https://www.patrickcoquart.com/",
        use_known_paths=False
    )
    for page in sitemap_tree.all_pages():
        links.append(page.url)
    console.print(f"[green]✅ {len(links)} URLs trouvées dans le sitemap[/green]")

    asyncio.run(concurrent_scraping(links)()


'''

class FrenchHTMLSimilarity:
    def __init__(self):
        self.model = SentenceTransformer(
            "dangvantuan/sentence-camembert-base", device="cuda"
        )
        self.console = Console()
        self.headers = HeaderGenerator()

    def extract_content(self, url):
        """Télécharge une page web et en extrait le contenu textuel principal."""
        try:
            response = requests.get(
                url=url,
                headers=self.headers.generate(),
                timeout=10,
                allow_redirects=True
            )
            response.raise_for_status()
            main_text = trafilatura.extract(response.text)

            return main_text if main_text else ""
        except requests.exceptions.RequestException as exc:
            self.console.print(
                f"[red]❌ An error occurred while requesting:[/red] {exc.request.url!r}."
            )

        return ""

    def compare_multiple_urls(self, urls):
        """Comparaison de toutes les paires d'URLs sans duplication"""

        self.console.print(
            Panel.fit("🔍 Extraction du contenu des pages", style="bold blue")
        )

        contents = {}

        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TaskProgressColumn(),
            console=self.console,
        ) as progress:
            task = progress.add_task("Extraction en cours...", total=len(urls))

            for url in urls:
                progress.update(task, description=f"Traitement: {url[:50]}...")
                content = self.extract_content(url)
                if content:
                    contents[url] = content
                    self.console.print(f"[green]✅ Contenu extrait:[/green] {url}")
                else:
                    self.console.print(
                        f"[red]❌ Impossible d'extraire le contenu de:[/red] {url}"
                    )
                progress.advance(task)

        if len(contents) < 2:
            self.console.print(
                Panel(
                    "[red]❌ Erreur: au moins 2 URLs valides sont nécessaires[/red]",
                    style="red",
                )
            )
            return {}

        url_pairs = list(itertools.combinations(contents.keys(), 2))

        self.console.print(
            Panel.fit(
                f"🧮 Calcul de la similarité pour {len(url_pairs)} paires d'URLs",
                style="bold green",
            )
        )

        all_contents = []
        pair_info = []

        for url1, url2 in url_pairs:
            content1 = contents[url1]
            content2 = contents[url2]

            all_contents.extend([content1, content2])
            pair_info.append((url1, url2))

        self.console.print(
            "[yellow]🤖 Calcul des embeddings avec le modèle CamemBERT...[/yellow]"
        )
        embeddings = self.model.encode(all_contents, show_progress_bar=True)

        results = {}

        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TaskProgressColumn(),
            console=self.console,
        ) as progress:
            task = progress.add_task("Calcul des similarités...", total=len(pair_info))

            for i, (url1, url2) in enumerate(pair_info):
                embedding1 = embeddings[i * 2]
                embedding2 = embeddings[i * 2 + 1]

                similarity = util.cos_sim(embedding1, embedding2).item()
                results[(url1, url2)] = similarity

                progress.advance(task)

        self.console.print(
            f"[bold green]✅ Analyse terminée ! {len(results)} comparaisons effectuées.[/bold green]"
        )
        return results


def create_similarity_graph(results):
    if not results:
        return nx.DiGraph()

    G = nx.DiGraph()

    all_urls = set()
    for url1, url2 in results.keys():
        all_urls.add(url1)
        all_urls.add(url2)

    G.add_nodes_from(all_urls)

    best_matches = {url: {"partner": None, "score": -1.0} for url in all_urls}

    for (url1, url2), score in results.items():
        if score > best_matches[url1]["score"]:
            best_matches[url1] = {"partner": url2, "score": score}

        if score > best_matches[url2]["score"]:
            best_matches[url2] = {"partner": url1, "score": score}

    for url, data in best_matches.items():
        if data["partner"]:
            G.add_edge(url, data["partner"], weight=data["score"])

    return G


def main():
    console = Console()

    console.print(
        Panel.fit("🚀 Analyseur de Similarité HTML Français", style="bold magenta")
    )

    comparator = FrenchHTMLSimilarity()

    console.print("[cyan]📡 Récupération des URLs depuis le sitemap...[/cyan]")
    links = []
    # sitemap_tree = sitemap_tree_for_homepage("https://zonetuto.fr/")
    sitemap_tree = sitemap_tree_for_homepage(
        homepage_url="https://www.patrickcoquart.com/",
        use_known_paths=False
    )
    for page in sitemap_tree.all_pages():
        links.append(page.url)
    console.print(f"[green]✅ {len(links)} URLs trouvées dans le sitemap[/green]")

    results_pairs = comparator.compare_multiple_urls(links)

    if results_pairs:
        console.print("[cyan]🕸️  Création du graphe de similarité...[/cyan]")
        sim_graph = create_similarity_graph(results_pairs)
        nx.write_gexf(sim_graph, "urls_similarity_graph.gexf")
        console.print(
            "[green]✅ Graphe sauvegardé dans 'urls_similarity_graph.gexf'[/green]"
        )
    else:
        console.print(Panel("[red]❌ Aucun résultat obtenu[/red]", style="red"))


if __name__ == "__main__":
    main()

'''